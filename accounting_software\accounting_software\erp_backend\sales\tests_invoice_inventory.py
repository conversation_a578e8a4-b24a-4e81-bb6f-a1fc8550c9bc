"""
Test cases for Invoice creation with inventory validation and sales tax integration
"""

from django.test import TestCase
from django.contrib.auth.models import User
from decimal import Decimal
from datetime import date

from sales.models import Product, ProductCategory, Invoice, InvoiceLineItem
from contacts.models import Contact
from inventory.models import Warehouse, Inventory, StockTransaction
from sales_tax.models import SalesTax


class InvoiceInventoryTestCase(TestCase):
    """Test invoice creation with inventory validation"""
    
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test customer
        self.customer = Contact.objects.create(
            name='Test Customer',
            contact_type='customer',
            email='<EMAIL>',
            phone='************'
        )
        
        # Create test warehouse
        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            warehouse_type='MAIN',
            location='123 Main St',
            created_by=self.user
        )
        
        # Create test product category
        self.category = ProductCategory.objects.create(
            name='Test Category',
            description='Test category for products'
        )
        
        # Create test product
        self.product = Product.objects.create(
            name='Test Product',
            sku='TEST-001',
            product_type='product',
            category=self.category,
            unit_price=Decimal('100.00'),
            cost_price=Decimal('60.00'),
            created_by=self.user
        )
        
        # Create inventory for the product
        self.inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('50.00'),
            quantity_reserved=Decimal('0.00'),
            reorder_point=Decimal('10.00'),
            reorder_quantity=Decimal('100.00')
        )
        
        # Create test sales tax
        self.sales_tax = SalesTax.objects.create(
            tax_type='output',
            description='Standard GST',
            rate=Decimal('10.000'),
            created_by=self.user
        )
    
    def test_invoice_creation_with_sufficient_inventory(self):
        """Test creating invoice when sufficient inventory is available"""
        # Create invoice
        invoice = Invoice.objects.create(
            customer=self.customer,
            warehouse=self.warehouse,
            invoice_date=date.today(),
            due_date=date.today(),
            status='draft',
            created_by=self.user
        )
        
        # Create invoice line item
        line_item = InvoiceLineItem.objects.create(
            invoice=invoice,
            product=self.product,
            description=self.product.name,
            quantity=Decimal('10.00'),
            unit_price=self.product.unit_price,
            sales_tax=self.sales_tax,
            taxable=True
        )
        
        # Validate inventory availability
        self.assertTrue(invoice.validate_inventory_availability())
        
        # Check line item calculations
        self.assertEqual(line_item.line_total, Decimal('1000.00'))  # 10 * 100
        self.assertEqual(line_item.tax_rate, Decimal('10.000'))
        self.assertEqual(line_item.tax_amount, Decimal('100.00'))  # 1000 * 0.10
    
    def test_invoice_creation_with_insufficient_inventory(self):
        """Test creating invoice when insufficient inventory is available"""
        # Create invoice
        invoice = Invoice.objects.create(
            customer=self.customer,
            warehouse=self.warehouse,
            invoice_date=date.today(),
            due_date=date.today(),
            status='draft',
            created_by=self.user
        )
        
        # Create invoice line item with quantity exceeding available inventory
        InvoiceLineItem.objects.create(
            invoice=invoice,
            product=self.product,
            description=self.product.name,
            quantity=Decimal('100.00'),  # More than available (50)
            unit_price=self.product.unit_price,
            sales_tax=self.sales_tax,
            taxable=True
        )
        
        # Validate inventory availability should fail
        with self.assertRaises(ValueError) as context:
            invoice.validate_inventory_availability()
        
        self.assertIn('Insufficient inventory', str(context.exception))
    
    def test_inventory_transaction_creation(self):
        """Test that inventory transactions are created when invoice is posted"""
        # Create invoice
        invoice = Invoice.objects.create(
            customer=self.customer,
            warehouse=self.warehouse,
            invoice_date=date.today(),
            due_date=date.today(),
            status='draft',
            created_by=self.user
        )
        
        # Create invoice line item
        InvoiceLineItem.objects.create(
            invoice=invoice,
            product=self.product,
            description=self.product.name,
            quantity=Decimal('10.00'),
            unit_price=self.product.unit_price,
            sales_tax=self.sales_tax,
            taxable=True
        )
        
        # Get initial inventory quantity
        initial_quantity = self.inventory.quantity_on_hand
        
        # Post inventory transactions
        invoice.post_inventory_transactions(self.user)
        
        # Refresh inventory from database
        self.inventory.refresh_from_db()
        
        # Check that inventory was reduced
        expected_quantity = initial_quantity - Decimal('10.00')
        self.assertEqual(self.inventory.quantity_on_hand, expected_quantity)
        
        # Check that stock transaction was created
        stock_transaction = StockTransaction.objects.filter(
            product=self.product,
            warehouse=self.warehouse,
            transaction_type='SALE',
            reference_type='INVOICE',
            reference_id=invoice.invoice_id
        ).first()
        
        self.assertIsNotNone(stock_transaction)
        self.assertEqual(stock_transaction.quantity, Decimal('-10.00'))  # Negative for outgoing
        self.assertEqual(stock_transaction.unit_price, self.product.unit_price)
    
    def test_service_product_no_inventory_validation(self):
        """Test that service products don't require inventory validation"""
        # Create service product
        service_product = Product.objects.create(
            name='Test Service',
            sku='SVC-001',
            product_type='service',
            category=self.category,
            unit_price=Decimal('200.00'),
            created_by=self.user
        )
        
        # Create invoice
        invoice = Invoice.objects.create(
            customer=self.customer,
            warehouse=self.warehouse,
            invoice_date=date.today(),
            due_date=date.today(),
            status='draft',
            created_by=self.user
        )
        
        # Create invoice line item for service
        InvoiceLineItem.objects.create(
            invoice=invoice,
            product=service_product,
            description=service_product.name,
            quantity=Decimal('5.00'),
            unit_price=service_product.unit_price,
            sales_tax=self.sales_tax,
            taxable=True
        )
        
        # Validate inventory availability should pass (services don't need inventory)
        self.assertTrue(invoice.validate_inventory_availability())
        
        # Post inventory transactions should not affect inventory for services
        invoice.post_inventory_transactions(self.user)
        
        # No stock transaction should be created for services
        stock_transaction = StockTransaction.objects.filter(
            product=service_product,
            transaction_type='SALE'
        ).first()
        
        self.assertIsNone(stock_transaction)
