#!/usr/bin/env python
"""
Test script to reproduce invoice creation issue
"""
import os
import django

# Setup Django - run from erp_backend directory
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import Invoice, InvoiceLineItem
from contacts.models import Customer
from inventory.models import Warehouse
from django.contrib.auth.models import User
from decimal import Decimal
import traceback

def test_invoice_creation():
    """Test creating an invoice to reproduce the error"""
    try:
        print("Testing invoice creation...")
        
        # Get test data
        user = User.objects.first()
        if not user:
            print("❌ No users found. Please create a user first.")
            return
        
        customer = Customer.objects.first()
        if not customer:
            print("❌ No customers found. Please create a customer first.")
            return
        
        warehouse = Warehouse.objects.first()
        if not warehouse:
            print("❌ No warehouses found. Please create a warehouse first.")
            return
        
        print(f"✅ Found user: {user.username}")
        print(f"✅ Found customer: {customer.contact.name}")
        print(f"✅ Found warehouse: {warehouse.name}")
        
        # Create invoice data similar to frontend
        invoice_data = {
            'customer': customer.contact,  # Use the contact directly
            'warehouse': warehouse,
            'invoice_date': '2025-06-28',
            'due_date': '2025-07-28',
            'status': 'draft',
            'subtotal': Decimal('100.00'),
            'tax_amount': Decimal('0.00'),
            'total_amount': Decimal('100.00'),
            'amount_paid': Decimal('0.00'),
            'memo': 'Test invoice',
            'message_to_customer': 'Test terms',
            'payment_terms': 'net_30',
            'created_by': user
        }
        
        print("Creating invoice...")
        invoice = Invoice.objects.create(**invoice_data)
        print(f"✅ Invoice created: {invoice.invoice_number}")
        
        # Create line item
        line_item_data = {
            'invoice': invoice,
            'description': 'Test service item',
            'quantity': Decimal('1.00'),
            'unit_price': Decimal('100.00'),
            'taxable': True
        }
        
        print("Creating line item...")
        line_item = InvoiceLineItem.objects.create(**line_item_data)
        print(f"✅ Line item created: {line_item.description}")
        
        # Test the serializer approach
        print("\nTesting with serializer...")
        from sales.serializers import InvoiceCreateUpdateSerializer
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.post('/api/sales/invoices/')
        request.user = user
        
        serializer_data = {
            'customer': customer.id,
            'warehouse': warehouse.id,
            'invoice_date': '2025-06-28',
            'due_date': '2025-07-28',
            'status': 'draft',
            'subtotal': 100.0,
            'tax_amount': 0.0,
            'total_amount': 100.0,
            'amount_paid': 0.0,
            'line_items': [
                {
                    'description': 'Test service item',
                    'quantity': 1.0,
                    'unit_price': 100.0,
                    'taxable': True
                }
            ],
            'memo': 'Test invoice',
            'message_to_customer': 'Test terms',
            'payment_terms': 'net_30'
        }
        
        serializer = InvoiceCreateUpdateSerializer(data=serializer_data, context={'request': request})
        
        if serializer.is_valid():
            invoice2 = serializer.save()
            print(f"✅ Serializer invoice created: {invoice2.invoice_number}")
        else:
            print(f"❌ Serializer validation errors: {serializer.errors}")
            
        print("\n✅ All tests passed!")
        
    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        print("Full traceback:")
        traceback.print_exc()

if __name__ == '__main__':
    test_invoice_creation()
