# Generated by Django 4.2.21 on 2025-06-28 14:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_alter_goodsreturnnote_vendor'),
        ('sales_tax', '0001_initial'),
        ('sales', '0011_update_customer_references'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='warehouse',
            field=models.ForeignKey(blank=True, help_text='Warehouse for inventory validation and stock reduction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='inventory.warehouse'),
        ),
        migrations.AddField(
            model_name='invoicelineitem',
            name='sales_tax',
            field=models.ForeignKey(blank=True, help_text='Sales tax from company setup (output tax only)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales_tax.salestax'),
        ),
    ]
