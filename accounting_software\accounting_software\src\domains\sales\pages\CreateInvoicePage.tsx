import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Typography,
  Alert,
  IconButton,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Divider,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  InputAdornment,
  Chip,
  Stack,
  useTheme,
  useMediaQuery,
  Container,
  Fab,
  Tooltip,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Badge,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Send as SendIcon,
  Print as PrintIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { invoiceService, Invoice, InvoiceItem, InvoiceCustomer } from '../../../services/invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';
import { customerService } from '../../../services/customer.service';
import { inventoryService } from '../../../domains/inventory/services/inventory.service';
import { salesTaxService, SalesTaxOption } from '../../../services/sales-tax.service';
import { ContactsService } from '../../../domains/contacts/services/contacts.service';
import api from '../../../services/api';
import JournalLineTable from '../../../shared/components/JournalLineTable';

const CreateInvoicePage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [customers, setCustomers] = useState<InvoiceCustomer[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [warehouses, setWarehouses] = useState<any[]>([]);
  const [salesTaxOptions, setSalesTaxOptions] = useState<SalesTaxOption[]>([]);
  const [inventoryStatus, setInventoryStatus] = useState<any[]>([]);
  
  // Form data
  const [formData, setFormData] = useState<Partial<Invoice>>({
    customer: 0,
    warehouse: 0,
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
    status: 'draft',
    items: [],
    subtotal: 0,
    tax_total: 0,
    total_amount: 0,
    notes: '',
    terms: 'Payment due within 30 days',
  });

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load customers
        try {
          const customersResponse = await ContactsService.getCustomers({ page_size: 1000, ordering: 'display_name' });
          const customersData = customersResponse.results || [];
          setCustomers(customersData.map((customer: any) => ({
            id: customer.id,
            display_name: customer.display_name || customer.name,
            email: customer.email,
            payment_terms: customer.payment_terms || 'net_30'
          })));
        } catch (err) {
          console.error('Error loading customers:', err);
          setCustomers([]);
        }

        // Load products
        try {
          const productsResponse = await api.get('/sales/products/?page_size=1000&ordering=name');
          const productsData = productsResponse.data.results || productsResponse.data || [];
          setProducts(productsData.map((product: any) => ({
            id: product.id,
            name: product.name,
            unit_price: product.unit_price || 0,
            cost_price: product.cost_price || 0,
            description: product.description || product.name,
            product_type: product.product_type || 'product',
            sku: product.sku
          })));
        } catch (err) {
          console.error('Error loading products:', err);
          setProducts([]);
        }

        // Load warehouses
        try {
          const warehousesResponse = await inventoryService.getAllWarehouses();
          console.log('Warehouses response:', warehousesResponse);
          const warehousesData = Array.isArray(warehousesResponse) ? warehousesResponse : [];
          console.log('Warehouses data:', warehousesData);
          setWarehouses(warehousesData.map((warehouse: any) => ({
            id: warehouse.warehouse_id || warehouse.id,
            name: warehouse.name,
            warehouse_code: warehouse.code || warehouse.warehouse_code,
            warehouse_type: warehouse.warehouse_type || 'MAIN'
          })));
        } catch (err) {
          console.error('Error loading warehouses:', err);
          setWarehouses([]);
        }

        // Load sales tax
        try {
          const salesTaxResponse = await salesTaxService.getAllSalesTaxes();
          const salesTaxData = Array.isArray(salesTaxResponse) ? salesTaxResponse : [];
          setSalesTaxOptions(salesTaxData.filter((tax: any) => tax.tax_type === 'output').map((tax: any) => ({
            id: tax.id,
            description: tax.description,
            rate: tax.rate,
            tax_type: tax.tax_type
          })));
        } catch (err) {
          console.error('Error loading sales tax:', err);
          setSalesTaxOptions([]);
        }

        // If editing, load the invoice
        if (isEditing && id) {
          try {
            const invoice = await invoiceService.getInvoice(parseInt(id));
            setFormData(invoice);
          } catch (err) {
            console.error('Error loading invoice:', err);
            setError('Failed to load invoice data');
          }
        }

      } catch (err) {
        console.error('Error in loadData:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isEditing]);

  // Handlers
  const handleFieldChange = (field: keyof Invoice, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calculate due date when customer changes
    if (field === 'customer' && value) {
      const customer = customers.find(c => c.id === value);
      if (customer?.payment_terms && formData.invoice_date) {
        const dueDate = invoiceService.calculateDueDate(formData.invoice_date, customer.payment_terms);
        setFormData(prev => ({ ...prev, due_date: dueDate }));
      }
    }
  };

  // Check inventory availability
  const checkInventoryAvailability = useCallback(async () => {
    if (!formData.warehouse || !formData.items?.length) return;

    try {
      // Mock inventory check for now
      const mockInventoryStatus = formData.items.map(item => {
        const product = products.find(p => p.id === item.product);
        if (product?.product_type === 'product') {
          const availableQty = Math.floor(Math.random() * 100) + 10; // Mock available quantity
          return {
            product_id: item.product,
            product_name: product.name,
            required_quantity: item.quantity,
            available_quantity: availableQty,
            sufficient: item.quantity <= availableQty
          };
        }
        return null;
      }).filter(Boolean);

      setInventoryStatus(mockInventoryStatus);
    } catch (error) {
      console.error('Failed to check inventory:', error);
    }
  }, [formData.warehouse, formData.items, products]);

  // Check inventory when warehouse or items change
  useEffect(() => {
    checkInventoryAvailability();
  }, [checkInventoryAvailability]);

  const addLineItem = () => {
    const newItem: InvoiceItem = {
      description: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      taxable: true,
      tax_rate: 0,
      tax_amount: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));
  };

  const updateLineItem = (index: number, field: keyof InvoiceItem, value: any) => {
    setFormData(prev => {
      const items = [...(prev.items || [])];
      items[index] = { ...items[index], [field]: value };

      // Auto-populate fields when product is selected
      if (field === 'product' && value) {
        const product = products.find(p => p.id === value);
        if (product) {
          items[index].description = product.name;
          items[index].unit_price = product.unit_price || 0;
          items[index].product_name = product.name;
        }
      }

      // Auto-set tax rate when sales tax is selected
      if (field === 'sales_tax' && value) {
        const salesTax = salesTaxOptions.find(st => st.id === value);
        if (salesTax) {
          items[index].tax_rate = salesTax.rate;
          items[index].sales_tax_description = salesTax.description;
        }
      }

      // Auto-calculate line total
      if (field === 'quantity' || field === 'unit_price' || field === 'product' || field === 'sales_tax') {
        const quantity = field === 'quantity' ? value : items[index].quantity;
        const unitPrice = field === 'unit_price' ? value : items[index].unit_price;
        items[index].line_total = quantity * unitPrice;

        // Calculate tax
        const taxRate = items[index].tax_rate || 0;
        const taxable = items[index].taxable !== false; // Default to true
        items[index].tax_amount = taxable ? (items[index].line_total * taxRate) / 100 : 0;
      }
      
      // Recalculate totals
      const totals = invoiceService.calculateTotals(items);
      
      return {
        ...prev,
        items,
        ...totals
      };
    });
  };

  const removeLineItem = (index: number) => {
    setFormData(prev => {
      const items = (prev.items || []).filter((_, i) => i !== index);
      const totals = invoiceService.calculateTotals(items);
      
      return {
        ...prev,
        items,
        ...totals
      };
    });
  };

  const handleProductSelect = (index: number, product: any) => {
    if (product) {
      updateLineItem(index, 'product', product.id);
      updateLineItem(index, 'product_name', product.name);
      updateLineItem(index, 'description', product.description || product.name);
      updateLineItem(index, 'unit_price', product.sales_price || 0);
    }
  };

  const handleSave = async (action: 'save' | 'save_new' | 'save_close' = 'save') => {
    try {
      setLoading(true);
      setError(null);

      // Validation
      if (!formData.customer) {
        throw new Error('Please select a customer');
      }
      if (!formData.warehouse) {
        throw new Error('Please select a warehouse');
      }
      if (!formData.items?.length) {
        throw new Error('Please add at least one line item');
      }

      const invoiceData = {
        customer: formData.customer!,
        warehouse: formData.warehouse!,
        invoice_date: formData.invoice_date!,
        due_date: formData.due_date!,
        status: formData.status!,
        items: formData.items!,
        subtotal: formData.subtotal || 0,
        tax_total: formData.tax_total || 0,
        total_amount: formData.total_amount || 0,
        notes: formData.notes || '',
        terms: formData.terms || '',
      };

      let savedInvoice: Invoice;
      if (isEditing && id) {
        savedInvoice = await invoiceService.updateInvoice(parseInt(id), invoiceData);
      } else {
        savedInvoice = await invoiceService.createInvoice(invoiceData);
      }

      // Handle different save actions
      switch (action) {
        case 'save_close':
          setSnackbar({
            open: true,
            message: `Invoice ${isEditing ? 'updated' : 'created'} successfully!`,
            severity: 'success'
          });
          setTimeout(() => navigate('/dashboard/sales/invoices'), 1000);
          break;

        case 'save_new':
          setSnackbar({
            open: true,
            message: `Invoice ${isEditing ? 'updated' : 'created'} successfully! Creating new invoice...`,
            severity: 'success'
          });
          setTimeout(() => {
            // Reset form for new invoice
            setFormData({
              customer: 0,
              warehouse: 0,
              invoice_date: dayjs().format('YYYY-MM-DD'),
              due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
              status: 'draft',
              items: [],
              subtotal: 0,
              tax_total: 0,
              total_amount: 0,
              notes: '',
              terms: 'Payment due within 30 days',
            });
            setInventoryStatus([]);
          }, 1000);
          break;

        case 'save':
        default:
          setError(null);
          setSnackbar({
            open: true,
            message: `Invoice ${isEditing ? 'updated' : 'created'} successfully!`,
            severity: 'success'
          });
          break;
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/sales/invoices');
  };

  const selectedCustomer = customers.find(c => c.id === formData.customer);

  return (
    <Container maxWidth={false} sx={{
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      py: 3,
      px: { xs: 1, sm: 2, md: 3 }
    }}>
      {/* Header */}
      <Paper elevation={0} sx={{
        mb: 3,
        p: 3,
        backgroundColor: 'white',
        borderRadius: 2,
        border: '1px solid #e0e0e0'
      }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <IconButton
              onClick={handleCancel}
              sx={{
                backgroundColor: '#f5f5f5',
                '&:hover': { backgroundColor: '#e0e0e0' }
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Box>
              <Typography variant="h4" fontWeight="600" color="primary.main">
                {isEditing ? 'Edit Invoice' : 'Create Invoice'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isEditing ? 'Update invoice details' : 'Create a new invoice for your customer'}
              </Typography>
            </Box>
          </Stack>

          <Stack direction="row" spacing={2}>
            {!isMobile && isEditing && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<PrintIcon />}
                  sx={{ borderRadius: 2 }}
                >
                  Print
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<SendIcon />}
                  sx={{ borderRadius: 2 }}
                >
                  Send
                </Button>
              </>
            )}
          </Stack>
        </Stack>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3, borderRadius: 2 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Main Invoice Form */}
      <Paper elevation={0} sx={{
        backgroundColor: 'white',
        borderRadius: 2,
        border: '1px solid #e0e0e0',
        overflow: 'hidden'
      }}>
        {/* Invoice Header Section */}
        <Box sx={{
          p: 4,
          backgroundColor: '#fafafa',
          borderBottom: '1px solid #e0e0e0'
        }}>
          <Grid container spacing={4}>
            {/* Customer & Warehouse Selection */}
            <Grid item xs={12} lg={8}>
              <Stack spacing={3}>
                <Box>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Customer Information
                  </Typography>
                  <Autocomplete
                    options={customers}
                    getOptionLabel={(option) => option.display_name || ''}
                    value={customers.find(c => c.id === formData.customer) || null}
                    onChange={(_, value) => handleFieldChange('customer', value?.id || 0)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select a customer"
                        variant="outlined"
                        fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                            backgroundColor: 'white'
                          }
                        }}
                      />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                          {option.display_name?.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body1">{option.display_name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {option.email}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                  />
                </Box>

                <Box>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    <BusinessIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Warehouse
                  </Typography>
                  <FormControl fullWidth>
                    <Select
                      value={formData.warehouse || ''}
                      onChange={(e) => handleFieldChange('warehouse', e.target.value)}
                      displayEmpty
                      sx={{
                        borderRadius: 2,
                        backgroundColor: 'white'
                      }}
                    >
                      <MenuItem value="">
                        <em>Select warehouse</em>
                      </MenuItem>
                      {warehouses.map((warehouse) => (
                        <MenuItem key={warehouse.id} value={warehouse.id}>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <InventoryIcon fontSize="small" />
                            <Box>
                              <Typography variant="body1">{warehouse.name}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {warehouse.warehouse_code} - {warehouse.warehouse_type}
                              </Typography>
                            </Box>
                          </Stack>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Stack>
            </Grid>

            {/* Invoice Details */}
            <Grid item xs={12} lg={4}>
              <Stack spacing={3}>
                <Box>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    <CalendarIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Invoice Details
                  </Typography>
                  <Stack spacing={2}>
                    <DatePicker
                      label="Invoice Date"
                      value={dayjs(formData.invoice_date)}
                      onChange={(date) => handleFieldChange('invoice_date', date?.format('YYYY-MM-DD'))}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          sx: {
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                              backgroundColor: 'white'
                            }
                          }
                        }
                      }}
                    />
                    <DatePicker
                      label="Due Date"
                      value={dayjs(formData.due_date)}
                      onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          sx: {
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                              backgroundColor: 'white'
                            }
                          }
                        }
                      }}
                    />
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={formData.status || 'draft'}
                        onChange={(e) => handleFieldChange('status', e.target.value)}
                        label="Status"
                        sx={{
                          borderRadius: 2,
                          backgroundColor: 'white'
                        }}
                      >
                        <MenuItem value="draft">
                          <Chip label="Draft" size="small" color="default" />
                        </MenuItem>
                        <MenuItem value="sent">
                          <Chip label="Sent" size="small" color="info" />
                        </MenuItem>
                        <MenuItem value="paid">
                          <Chip label="Paid" size="small" color="success" />
                        </MenuItem>
                        <MenuItem value="overdue">
                          <Chip label="Overdue" size="small" color="error" />
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Stack>
                </Box>
              </Stack>
            </Grid>
          </Grid>
        </Box>

        {/* Line Items Section */}
        <Box sx={{ p: 4 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
            <Typography variant="h6" fontWeight="600">
              <ReceiptIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Line Items
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addLineItem}
              sx={{
                borderRadius: 2,
                fontWeight: 600
              }}
            >
              Add Item
            </Button>
          </Stack>

          {/* Inventory Status Alert */}
          {inventoryStatus.length > 0 && (
            <Alert
              severity="info"
              sx={{ mb: 3, borderRadius: 2 }}
              icon={<InventoryIcon />}
            >
              <Typography variant="subtitle2" gutterBottom>
                Inventory Status
              </Typography>
              {inventoryStatus.map((status, index) => (
                <Stack key={index} direction="row" alignItems="center" spacing={1}>
                  {status.sufficient ? (
                    <CheckCircleIcon color="success" fontSize="small" />
                  ) : (
                    <WarningIcon color="error" fontSize="small" />
                  )}
                  <Typography variant="body2">
                    {status.product_name}: {status.available_quantity} available, {status.required_quantity} required
                  </Typography>
                </Stack>
              ))}
            </Alert>
          )}

          {/* Line Items Table */}
          <TableContainer
            component={Paper}
            elevation={0}
            sx={{
              border: '1px solid #e0e0e0',
              borderRadius: 2,
              overflow: 'hidden'
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell sx={{ fontWeight: 600, py: 2 }}>Product</TableCell>
                  <TableCell sx={{ fontWeight: 600, py: 2, minWidth: 200 }}>Description</TableCell>
                  {!isMobile && <TableCell sx={{ fontWeight: 600, py: 2, width: 120 }}>Quantity</TableCell>}
                  {!isMobile && <TableCell sx={{ fontWeight: 600, py: 2, width: 140 }}>Unit Price</TableCell>}
                  {!isMobile && <TableCell sx={{ fontWeight: 600, py: 2, width: 160 }}>Sales Tax</TableCell>}
                  <TableCell sx={{ fontWeight: 600, py: 2, width: 140 }}>Total</TableCell>
                  <TableCell sx={{ fontWeight: 600, py: 2, width: 60 }}></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {formData.items?.map((item, index) => (
                  <TableRow key={index} sx={{ '&:hover': { backgroundColor: '#f8f9fa' } }}>
                    {/* Product Selection */}
                    <TableCell sx={{ py: 2 }}>
                      <Autocomplete
                        options={products}
                        getOptionLabel={(option) => option.name || ''}
                        value={products.find(p => p.id === item.product) || null}
                        onChange={(_, value) => updateLineItem(index, 'product', value?.id || null)}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Select product"
                            size="small"
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                borderRadius: 1
                              }
                            }}
                          />
                        )}
                        renderOption={(props, option) => (
                          <Box component="li" {...props}>
                            <Stack>
                              <Typography variant="body2">{option.name}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                ${option.unit_price} - {option.product_type}
                              </Typography>
                            </Stack>
                          </Box>
                        )}
                      />
                    </TableCell>

                    {/* Description */}
                    <TableCell sx={{ py: 2 }}>
                      <TextField
                        value={item.description || ''}
                        onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                        placeholder="Item description"
                        size="small"
                        fullWidth
                        multiline
                        maxRows={2}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 1
                          }
                        }}
                      />
                    </TableCell>

                    {/* Quantity - Hidden on mobile */}
                    {!isMobile && (
                      <TableCell sx={{ py: 2 }}>
                        <TextField
                          type="number"
                          value={item.quantity || 0}
                          onChange={(e) => updateLineItem(index, 'quantity', Number(e.target.value))}
                          inputProps={{ min: 0, step: 0.01 }}
                          size="small"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1
                            }
                          }}
                        />
                      </TableCell>
                    )}

                    {/* Unit Price - Hidden on mobile */}
                    {!isMobile && (
                      <TableCell sx={{ py: 2 }}>
                        <FormattedCurrencyInput
                          value={item.unit_price || 0}
                          onChange={(value) => updateLineItem(index, 'unit_price', value)}
                          size="small"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1
                            }
                          }}
                        />
                      </TableCell>
                    )}

                    {/* Sales Tax - Hidden on mobile */}
                    {!isMobile && (
                      <TableCell sx={{ py: 2 }}>
                        <FormControl size="small" fullWidth>
                          <Select
                            value={item.sales_tax || ''}
                            onChange={(e) => updateLineItem(index, 'sales_tax', e.target.value)}
                            displayEmpty
                            sx={{ borderRadius: 1 }}
                          >
                            <MenuItem value="">
                              <em>No tax</em>
                            </MenuItem>
                            {salesTaxOptions.map((tax) => (
                              <MenuItem key={tax.id} value={tax.id}>
                                {tax.description} ({tax.rate}%)
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </TableCell>
                    )}

                    {/* Line Total */}
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="600">
                        {formatCurrency(item.line_total || 0)}
                      </Typography>
                      {item.tax_amount > 0 && (
                        <Typography variant="caption" color="text.secondary">
                          Tax: {formatCurrency(item.tax_amount)}
                        </Typography>
                      )}
                    </TableCell>

                    {/* Actions */}
                    <TableCell sx={{ py: 2 }}>
                      <IconButton
                        onClick={() => removeLineItem(index)}
                        size="small"
                        color="error"
                        sx={{
                          '&:hover': {
                            backgroundColor: 'error.light',
                            color: 'white'
                          }
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}

                {/* Empty State */}
                {!formData.items?.length && (
                  <TableRow>
                    <TableCell colSpan={isMobile ? 4 : 7} align="center" sx={{ py: 6 }}>
                      <Stack alignItems="center" spacing={2}>
                        <ReceiptIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                        <Typography color="text.secondary" variant="h6">
                          No items added yet
                        </Typography>
                        <Typography color="text.secondary" variant="body2">
                          Click "Add Item" to start building your invoice
                        </Typography>
                      </Stack>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>

        {/* Totals Section */}
        {(formData.items?.length || 0) > 0 && (
          <Box sx={{
            p: 4,
            backgroundColor: '#fafafa',
            borderTop: '1px solid #e0e0e0'
          }}>
            <Grid container justifyContent="flex-end">
              <Grid item xs={12} sm={6} md={4}>
                <Paper elevation={0} sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                  <Stack spacing={2}>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body1">Subtotal:</Typography>
                      <Typography variant="body1" fontWeight="500">
                        {formatCurrency(formData.subtotal || 0)}
                      </Typography>
                    </Stack>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body1">Tax Total:</Typography>
                      <Typography variant="body1" fontWeight="500">
                        {formatCurrency(formData.tax_total || 0)}
                      </Typography>
                    </Stack>
                    <Divider />
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="h6" fontWeight="600">Total Amount:</Typography>
                      <Typography variant="h6" fontWeight="600" color="primary.main">
                        {formatCurrency(formData.total_amount || 0)}
                      </Typography>
                    </Stack>
                  </Stack>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Notes and Terms Section */}
        <Box sx={{ p: 4, borderTop: '1px solid #e0e0e0' }}>
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Additional Information
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={4}
                value={formData.notes || ''}
                onChange={(e) => handleFieldChange('notes', e.target.value)}
                placeholder="Add any notes for this invoice..."
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'white'
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Terms & Conditions"
                multiline
                rows={4}
                value={formData.terms || ''}
                onChange={(e) => handleFieldChange('terms', e.target.value)}
                placeholder="Payment terms and conditions..."
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'white'
                  }
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Bottom Action Buttons */}
      <Paper
        elevation={3}
        sx={{
          position: 'sticky',
          bottom: 0,
          mt: 4,
          p: 3,
          backgroundColor: 'white',
          borderTop: '1px solid #e0e0e0',
          borderRadius: '16px 16px 0 0',
          zIndex: 100
        }}
      >
        <Stack
          direction={isMobile ? 'column' : 'row'}
          justifyContent="space-between"
          alignItems={isMobile ? 'stretch' : 'center'}
          spacing={2}
        >
          <Button
            variant="outlined"
            onClick={handleCancel}
            startIcon={<ArrowBackIcon />}
            disabled={loading}
            sx={{
              borderRadius: 2,
              py: 1.5,
              px: 3,
              fontWeight: 600,
              order: isMobile ? 4 : 1
            }}
          >
            Cancel
          </Button>

          <Stack
            direction={isMobile ? 'column' : 'row'}
            spacing={2}
            sx={{ order: isMobile ? 1 : 2 }}
          >
            <Button
              variant="outlined"
              onClick={() => handleSave('save')}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
              sx={{
                borderRadius: 2,
                py: 1.5,
                px: 3,
                fontWeight: 600,
                minWidth: 120
              }}
            >
              {loading ? 'Saving...' : 'Save'}
            </Button>

            {!isEditing && (
              <Button
                variant="outlined"
                onClick={() => handleSave('save_new')}
                disabled={loading}
                startIcon={<AddIcon />}
                sx={{
                  borderRadius: 2,
                  py: 1.5,
                  px: 3,
                  fontWeight: 600,
                  minWidth: 140
                }}
              >
                Save & New
              </Button>
            )}

            <Button
              variant="contained"
              onClick={() => handleSave('save_close')}
              disabled={loading}
              startIcon={<CheckCircleIcon />}
              sx={{
                borderRadius: 2,
                py: 1.5,
                px: 3,
                fontWeight: 600,
                minWidth: 140
              }}
            >
              Save & Close
            </Button>
          </Stack>
        </Stack>
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default CreateInvoicePage; 