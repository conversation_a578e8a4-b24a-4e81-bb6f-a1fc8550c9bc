#!/usr/bin/env python
"""
Simple test script to verify invoice functionality with inventory validation
"""

import os
import sys
import django
from decimal import Decimal
from datetime import date

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from django.contrib.auth.models import User
from sales.models import Product, ProductCategory, Invoice, InvoiceLineItem
from contacts.models import Contact
from inventory.models import Warehouse, Inventory
from sales_tax.models import SalesTax


def create_test_data():
    """Create test data for invoice functionality"""
    print("Creating test data...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    print(f"✓ User: {user.username}")
    
    # Create test customer
    customer, created = Contact.objects.get_or_create(
        name='Test Customer',
        defaults={
            'contact_type': 'customer',
            'email': '<EMAIL>',
            'phone': '************'
        }
    )
    print(f"✓ Customer: {customer.name}")
    
    # Create test warehouse
    warehouse, created = Warehouse.objects.get_or_create(
        name='Main Warehouse',
        defaults={
            'warehouse_code': 'MAIN',
            'warehouse_type': 'MAIN',
            'location': '123 Main St',
            'created_by': user
        }
    )
    print(f"✓ Warehouse: {warehouse.name}")
    
    # Create test product category
    category, created = ProductCategory.objects.get_or_create(
        name='Test Category',
        defaults={'description': 'Test category for products'}
    )
    print(f"✓ Category: {category.name}")
    
    # Create test product
    product, created = Product.objects.get_or_create(
        name='Test Product',
        defaults={
            'sku': 'TEST-001',
            'product_type': 'product',
            'category': category,
            'unit_price': Decimal('100.00'),
            'cost_price': Decimal('60.00'),
            'created_by': user
        }
    )
    print(f"✓ Product: {product.name}")
    
    # Create inventory for the product
    inventory, created = Inventory.objects.get_or_create(
        product=product,
        warehouse=warehouse,
        defaults={
            'quantity_on_hand': Decimal('50.00'),
            'quantity_reserved': Decimal('0.00'),
            'reorder_point': Decimal('10.00'),
            'reorder_quantity': Decimal('100.00')
        }
    )
    print(f"✓ Inventory: {inventory.quantity_on_hand} units")
    
    # Create test sales tax
    sales_tax, created = SalesTax.objects.get_or_create(
        tax_type='output',
        description='Standard GST',
        defaults={
            'rate': Decimal('10.000'),
            'created_by': user
        }
    )
    print(f"✓ Sales Tax: {sales_tax.description} ({sales_tax.rate}%)")
    
    return user, customer, warehouse, product, inventory, sales_tax


def test_invoice_creation():
    """Test invoice creation with inventory validation"""
    print("\n" + "="*50)
    print("TESTING INVOICE CREATION")
    print("="*50)
    
    user, customer, warehouse, product, inventory, sales_tax = create_test_data()
    
    # Test 1: Create invoice with sufficient inventory
    print("\nTest 1: Invoice with sufficient inventory")
    try:
        invoice = Invoice.objects.create(
            customer=customer,
            warehouse=warehouse,
            invoice_date=date.today(),
            due_date=date.today(),
            status='draft',
            created_by=user
        )
        
        line_item = InvoiceLineItem.objects.create(
            invoice=invoice,
            product=product,
            description=product.name,
            quantity=Decimal('10.00'),
            unit_price=product.unit_price,
            sales_tax=sales_tax,
            taxable=True
        )
        
        # Check calculations
        print(f"  Line total: ${line_item.line_total}")
        print(f"  Tax rate: {line_item.tax_rate}%")
        print(f"  Tax amount: ${line_item.tax_amount}")
        
        # Validate inventory
        invoice.validate_inventory_availability()
        print("  ✓ Inventory validation passed")
        
        # Test inventory posting
        initial_qty = inventory.quantity_on_hand
        invoice.post_inventory_transactions(user)
        inventory.refresh_from_db()
        
        print(f"  Initial inventory: {initial_qty}")
        print(f"  Final inventory: {inventory.quantity_on_hand}")
        print("  ✓ Inventory transactions posted successfully")
        
    except Exception as e:
        print(f"  ✗ Error: {e}")
    
    # Test 2: Create invoice with insufficient inventory
    print("\nTest 2: Invoice with insufficient inventory")
    try:
        invoice2 = Invoice.objects.create(
            customer=customer,
            warehouse=warehouse,
            invoice_date=date.today(),
            due_date=date.today(),
            status='draft',
            created_by=user
        )
        
        InvoiceLineItem.objects.create(
            invoice=invoice2,
            product=product,
            description=product.name,
            quantity=Decimal('100.00'),  # More than available
            unit_price=product.unit_price,
            sales_tax=sales_tax,
            taxable=True
        )
        
        # This should fail
        invoice2.validate_inventory_availability()
        print("  ✗ Validation should have failed!")
        
    except ValueError as e:
        print(f"  ✓ Validation correctly failed: {e}")
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")


if __name__ == '__main__':
    test_invoice_creation()
    print("\n" + "="*50)
    print("TESTING COMPLETE")
    print("="*50)
