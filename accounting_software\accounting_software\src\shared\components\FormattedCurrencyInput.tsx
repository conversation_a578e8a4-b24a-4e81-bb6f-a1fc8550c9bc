import React, { forwardRef, useState } from 'react';
import { TextField, TextFieldProps } from '@mui/material';
import { NumericFormat, NumericFormatProps } from 'react-number-format';

interface CustomProps {
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
  currencySymbol?: string;
  decimalScale?: number;
  allowNegative?: boolean;
}

const NumericFormatCustom = forwardRef<
  NumericFormatProps,
  CustomProps & NumericFormatProps
>(function NumericFormatCustom(props, ref) {
  const { onChange, currencySymbol = '', decimalScale = 2, allowNegative = false, ...other } = props;

  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value || '', // Keep empty string for zero values
          },
        });
      }}
      thousandSeparator=","
      decimalSeparator="."
      decimalScale={decimalScale}
      fixedDecimalScale={false} // Don't force decimal places on display
      allowNegative={allowNegative}
      placeholder="" // Empty placeholder like QuickBooks
      // Ensure value is always a string and handle all edge cases
      value={(() => {
        const val = other.value;
        if (val === null || val === undefined) return '';
        if (typeof val === 'string') return val;
        if (typeof val === 'number') return isNaN(val) ? '' : val.toString();
        return String(val);
      })()}
    />
  );
});

interface FormattedCurrencyInputProps extends Omit<TextFieldProps, 'onChange'> {
  value: number | string;
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
  currencySymbol?: string;
  decimalScale?: number;
  allowNegative?: boolean;
}

const FormattedCurrencyInput: React.FC<FormattedCurrencyInputProps> = ({
  value,
  onChange,
  name,
  currencySymbol = '',
  decimalScale = 2,
  allowNegative = false,
  ...textFieldProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  // Safely convert value to string for react-number-format
  const safeValue = React.useMemo(() => {
    if (value === null || value === undefined || value === '') {
      return '';
    }

    if (typeof value === 'string') {
      // If it's already a string, ensure it's a valid number string
      const parsed = parseFloat(value);
      return isNaN(parsed) ? '' : parsed.toString();
    }

    if (typeof value === 'number') {
      return isNaN(value) ? '' : value.toString();
    }

    return '';
  }, [value]);

  // For display: show blank if zero (QuickBooks style)
  const displayValue = safeValue === '' || parseFloat(safeValue) === 0 ? '' : safeValue;

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    if (textFieldProps.onFocus) {
      textFieldProps.onFocus(event);
    }
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    if (textFieldProps.onBlur) {
      textFieldProps.onBlur(event);
    }
  };

  return (
    <TextField
      {...textFieldProps}
      name={name}
      value={displayValue}
      onChange={onChange}
      onFocus={handleFocus}
      onBlur={handleBlur}
      InputProps={{
        ...textFieldProps.InputProps,
        inputComponent: NumericFormatCustom as any,
        inputProps: {
          ...textFieldProps.InputProps?.inputProps,
          currencySymbol,
          decimalScale,
          allowNegative,
        },
      }}
      sx={{
        ...textFieldProps.sx,
        '& .MuiInputBase-input': {
          textAlign: 'right',
        },
      }}
    />
  );
};

export default FormattedCurrencyInput; 